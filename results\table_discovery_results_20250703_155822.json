{"discovery_metadata": {"timestamp": "2025-07-03T15:58:22.247322", "discovery_duration_seconds": 0, "devo_endpoint": "https://api-apac.devo.com/search/query", "discovery_version": "1.0.0"}, "statistics": {"total_tables_found": 28, "successful_discoveries": 28, "failed_discoveries": 0, "total_estimated_records": 0, "discovery_errors": []}, "tables": {"app.web.error": {"table_name": "app.web.error", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:13.394505", "count_period": "current_day"}, "cloud.aws.cloudtrail": {"table_name": "cloud.aws.cloudtrail", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:13.397242", "count_period": "current_day"}, "app.database.audit": {"table_name": "app.database.audit", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:13.388737", "count_period": "current_day"}, "app.api.requests": {"table_name": "app.api.requests", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:13.387137", "count_period": "current_day"}, "app.web.access": {"table_name": "app.web.access", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:13.391380", "count_period": "current_day"}, "firewall.all": {"table_name": "firewall.all", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:14.857503", "count_period": "current_day"}, "cloud.azure.activity": {"table_name": "cloud.azure.activity", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:14.800454", "count_period": "current_day"}, "custom.logs.info": {"table_name": "custom.logs.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:14.841580", "count_period": "current_day"}, "cloud.gcp.audit": {"table_name": "cloud.gcp.audit", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:14.818488", "count_period": "current_day"}, "firewall.cisco.asa": {"table_name": "firewall.cisco.asa", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:14.925482", "count_period": "current_day"}, "my.app.data": {"table_name": "my.app.data", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:16.371535", "count_period": "current_day"}, "firewall.fortinet": {"table_name": "firewall.fortinet", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:16.281223", "count_period": "current_day"}, "network.cisco.router": {"table_name": "network.cisco.router", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:16.395753", "count_period": "current_day"}, "network.switch.info": {"table_name": "network.switch.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:16.474166", "count_period": "current_day"}, "firewall.paloalto": {"table_name": "firewall.paloalto", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:16.319099", "count_period": "current_day"}, "siem.logtrust.auth.info": {"table_name": "siem.logtrust.auth.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:17.759998", "count_period": "current_day"}, "siem.logtrust.dns.info": {"table_name": "siem.logtrust.dns.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:17.806844", "count_period": "current_day"}, "siem.logtrust.alert.info": {"table_name": "siem.logtrust.alert.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:17.734555", "count_period": "current_day"}, "siem.logtrust.file.info": {"table_name": "siem.logtrust.file.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:17.889099", "count_period": "current_day"}, "siem.logtrust.network.info": {"table_name": "siem.logtrust.network.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:17.979407", "count_period": "current_day"}, "siem.logtrust.registry.info": {"table_name": "siem.logtrust.registry.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:19.169316", "count_period": "current_day"}, "siem.logtrust.process.info": {"table_name": "siem.logtrust.process.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:19.083321", "count_period": "current_day"}, "siem.logtrust.web.activity": {"table_name": "siem.logtrust.web.activity", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:19.320068", "count_period": "current_day"}, "siem.logtrust.system.info": {"table_name": "siem.logtrust.system.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:19.304732", "count_period": "current_day"}, "system.auth.login": {"table_name": "system.auth.login", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:19.349284", "count_period": "current_day"}, "system.linux.syslog": {"table_name": "system.linux.syslog", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:20.511262", "count_period": "current_day"}, "system.windows.eventlog": {"table_name": "system.windows.eventlog", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:20.552996", "count_period": "current_day"}, "user.activity.log": {"table_name": "user.activity.log", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T15:58:20.667858", "count_period": "current_day"}}}